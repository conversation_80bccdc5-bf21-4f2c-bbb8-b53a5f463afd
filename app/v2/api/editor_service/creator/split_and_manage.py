"""
Helper functions for splitting and managing curated content generation.

This module provides utilities to split generated tasks into proper database structures
and manage media generation for task items and story items.
"""

from datetime import datetime, timezone
from bson import ObjectId
from typing import Dict, Any, List, Tuple
import base64
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from fastapi import HTTPException

loggers = setup_new_logging(__name__)





async def split_and_manage(generated_tasks: Dict[str, Any], theme_id: str = None) -> Tuple[Dict[str, Any], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Split generated tasks into task_set, task_items, and story items following v2 audio process structure.

    Args:
        generated_tasks: Output from generate_tasks function
        theme_id: Optional theme ID to associate with the task set

    Returns:
        Tuple of (task_set_dict, task_items_list, story_items_list)
    """
    try:
        loggers.info("Starting split_and_manage for curated content")
        loggers.info(f"Input generated_tasks type: {type(generated_tasks)}")
        loggers.info(f"Input generated_tasks keys: {list(generated_tasks.keys()) if isinstance(generated_tasks, dict) else 'Not a dict'}")

        # Extract basic information
        title = generated_tasks.get("title", "Generated Curated Content 1")
        # Handle both 'output' and 'tasks' field names (AI might use either)
        tasks_data = generated_tasks.get("output") 
        
        # metadata = generated_tasks.get("metadata", {})

        loggers.info(f"Extracted title: {title}")
        loggers.info(f"Tasks data type: {type(tasks_data)}")
        loggers.info(f"Tasks data length: {len(tasks_data) if isinstance(tasks_data, list) else 'Not a list'}")
        loggers.info(f"Tasks data: {tasks_data[:100]}...")
        if not tasks_data:
            raise ValueError("No tasks data found in generated_tasks")

        # Handle nested structure: output is a list of chapters, each with tasks
        tasks = []
        if isinstance(tasks_data, list):
            loggers.info(f"Processing {len(tasks_data)} chapters")
            for i, chapter in enumerate(tasks_data):
                loggers.info(f"Chapter {i}: type={type(chapter)}, keys={list(chapter.keys()) if isinstance(chapter, dict) else 'Not a dict'}")
                if isinstance(chapter, dict) and "tasks" in chapter:
                    # Each chapter has a list of tasks
                    chapter_tasks = chapter["tasks"]
                    loggers.info(f"Chapter {i} tasks: type={type(chapter_tasks)}, count={len(chapter_tasks) if isinstance(chapter_tasks, list) else 'Not a list'}")
                    if isinstance(chapter_tasks, list):
                        tasks.extend(chapter_tasks)
                    else:
                        tasks.append(chapter_tasks)
                elif isinstance(chapter, dict):
                    # If chapter itself is a task
                    loggers.info(f"Chapter {i} is a task itself")
                    tasks.append(chapter)
        else:
            # If tasks_data is not a list, treat it as tasks directly
            tasks = tasks_data if isinstance(tasks_data, list) else [tasks_data]

        loggers.info(f"Final tasks count: {len(tasks)}")
        if not tasks:
            raise ValueError("No individual tasks found in generated content")

        # Separate text tasks from media tasks (following v2 pattern)
        text_tasks = []
        media_tasks = []

        for task in tasks:
            question_data = task.get("question", {})
            task_type = question_data.get("type", "single_choice")

            if task_type in ["speak_word", "word_identification", "image_identification"]:
                media_tasks.append(task)
            else:
                text_tasks.append(task)

        # Extract thumbnail from generated tasks if available
        # The thumbnail should be a keyword for image generation (like "Village Icon")
        thumbnail = generated_tasks.get("thumbnail", title)  # Use title as fallback
        # generate image of the thumbnail keyword'
        loggers.info(f"Using thumbnail keyword: {thumbnail}")
        # Extract all metadata from generated tasks
        title_en = generated_tasks.get("title_en", "")
        description = generated_tasks.get("description", "")
        description_en = generated_tasks.get("description_en", "")

        # Extract engagement questions from generated tasks (should be included in AI generation)
        engagement_questions = generated_tasks.get("engagement_questions", [])

        loggers.info(f"Extracted metadata - title_en: {title_en}, description: {description}, description_en: {description_en}")
        loggers.info(f"Extracted engagement_questions: {len(engagement_questions)} questions")
        loggers.info(f"Theme ID received: {theme_id} (type: {type(theme_id)})")

        # Create task set structure (following v2 task_sets collection structure)
        task_set = {
            "title": title,
            "title_en": title_en,  # Copy English title
            "thumbnail": thumbnail,  # Thumbnail keyword for image generation
            "description": description,  # Copy description
            "description_en": description_en,  # Copy English description
            "theme_id": ObjectId(theme_id) if theme_id else None,  # Add theme_id field (handle None case)
            "input_type": "audio",  # Curated content starts from text prompt
            "tasks": [],  # Will be populated with task IDs after insertion
            "stories": [],  # Will be populated with story IDs after insertion
            "engagement_questions": engagement_questions,  # Add engagement questions from AI generation
            "total_tasks": len(tasks),
            "total_stories": len(tasks),  # Each task has a story
            "text_tasks_ready": len(text_tasks),
            "media_tasks_pending": len(media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "gentype": "primary",
            "has_follow_up": False,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "usage_metadata": generated_tasks.get("metadata", {})
        }

        loggers.info(f"Task set created with thumbnail keyword: '{thumbnail}' for image generation")
        loggers.info(f"Added {len(engagement_questions)} engagement questions from AI generation")

        # Create task items (following v2 task_items collection structure)
        task_items = []
        for i, task in enumerate(tasks):
            question_data = task.get("question", {})
            task_type = question_data.get("type", "single_choice")

            # Copy all metadata from original task including audio options, image metadata, etc.
            original_metadata = task.get("metadata", {})
            question_metadata = question_data.get("metadata", {})

            # Create task item following exact v2 structure
            task_item = {
                "type": task_type,
                "title": task.get("title", f"Task {i + 1}"),
                "question": {
                    "type": task_type,
                    "text": question_data.get("text", ""),
                    "translated_text": question_data.get("translated_text", ""),
                    "options": question_data.get("options", {}),
                    "options_en": question_data.get("options_en", {}),  # New field for English options
                    "answer_hint": question_data.get("answer_hint", ""),
                    "metadata": question_metadata,  # Copy original question metadata
                    # Copy audio options metadata if present
                    "options_metadata": question_data.get("options_metadata", {}),
                    "audio_metadata": question_data.get("audio_metadata", {}),
                    "image_metadata": question_data.get("image_metadata", {})
                },
                "correct_answer": {
                    "type": task_type,
                    "value": question_data.get("answer", "")
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": task.get("max_score", 10),
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": task.get("complexity", 1),
                "metadata": {
                    "_media_ready": False,
                    "_priority": "media_pending" if task_type in ["speak_word", "word_identification", "image_identification"] else "text_ready",
                    "status": "generating",  # Universal root status - default to generating
                    **original_metadata  # Copy any additional metadata from original task
                }
            }
            task_items.append(task_item)

        # Create story items (following v2 story_steps collection structure)
        story_items = []
        for i, task in enumerate(tasks):
            story_data = task.get("story", {})

            # Copy all metadata from original story including audio metadata, image metadata, etc.
            original_story_metadata = story_data.get("metadata", {})
            original_audio_metadata = story_data.get("audio_metadata", {})
            original_image_metadata = story_data.get("image_metadata", {})

            # Create story item following exact v2 structure
            story_item = {
                "type": "story",  # Add type field for curated content items
                "stage": i + 1,
                "script": story_data.get("script", ""),
                "image": story_data.get("image", ""),  # Image description
                "thumbnail": story_data.get("thumbnail", "📖"),
                "audio_metadata": {
                    "_audio_ready": False,
                    "_priority": "audio_pending",
                    **original_audio_metadata  # Copy original audio metadata
                },
                "image_metadata": {
                    "_image_ready": False,
                    "_priority": "image_pending",
                    **original_image_metadata  # Copy original image metadata
                },
                "metadata": {
                    "status": "generating",  # Universal root status - default to generating
                    **original_story_metadata  # Copy any additional metadata from original story
                }
            }
            story_items.append(story_item)

        loggers.info(f"Split complete: {len(task_items)} task items ({len(text_tasks)} text, {len(media_tasks)} media), {len(story_items)} story items")
        return task_set, task_items, story_items

    except Exception as e:
        loggers.error(f"Error in split_and_manage: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to split and manage tasks: {str(e)}")


# Old media generation functions removed - now using v2 pattern with background generation


# Old story media generation function removed - now using v2 pattern with background generation


async def save_task_set_to_db(
    current_user: UserTenantDB,
    task_set: Dict[str, Any],
    task_set_id: ObjectId,
    story_script: str,
    audio_data: bytes,
    story_prompt: str,
    audio_storage_info: Dict[str, Any] = None
) -> ObjectId:
    """
    Save task set to curated_content_set collection following v2 pattern.

    Args:
        current_user: User context
        task_set: Task set data
        task_set_id: Task set ID
        story_script: Generated story script
        audio_data: Generated audio data
        story_prompt: Original story prompt

    Returns:
        ObjectId of saved task set
    """
    try:
        # Create input_content following v2 pattern with both script and audio info
        input_content = {
            "type": "curated_content",
            "original_prompt": story_prompt,
            "script": story_script,
            "script_length": len(story_script),
            "audio_length_bytes": len(audio_data) if audio_data else 0,
            "content_type": "text/plain",
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Add audio storage information if provided (following v2 pattern)
        if audio_storage_info:
            input_content.update(audio_storage_info)

        # Create the content set document following v2 task_sets structure
        content_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "title": task_set["title"],
            "title_en": task_set.get("title_en", ""),  # Add English title
            "thumbnail": task_set.get("thumbnail"),  # Add thumbnail field
            "description": task_set.get("description", ""),  # Add description
            "description_en": task_set.get("description_en", ""),  # Add English description
            "engagement_questions": task_set.get("engagement_questions", []),  # Add engagement questions
            "theme_id": task_set.get("theme_id"),  # Add theme_id field
            "input_type": "audio",
            "input_content": input_content,  # Now contains both script and audio info
            "tasks": task_set["tasks"],  # Will be updated with task IDs
            "stories": task_set["stories"],  # Will be updated with story IDs
            "total_tasks": task_set["total_tasks"],
            "total_stories": task_set["total_stories"],
            "text_tasks_ready": task_set["text_tasks_ready"],
            "media_tasks_pending": task_set["media_tasks_pending"],
            "attempted_tasks": task_set["attempted_tasks"],
            "total_verified": task_set["total_verified"],
            "status": task_set["status"],
            "gentype": task_set["gentype"],
            "has_follow_up": task_set["has_follow_up"],
            "total_score": task_set["total_score"],
            "scored": task_set["scored"],
            "attempts_count": task_set["attempts_count"],
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Insert the content set
        await current_user.async_db.curated_content_set.insert_one(content_set_doc)
        loggers.info(f"Saved curated content set with ID: {task_set_id}")

        return task_set_id

    except Exception as e:
        loggers.error(f"Error saving task set to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task set: {str(e)}")

async def save_task_items_to_db(
    current_user: UserTenantDB,
    task_items: List[Dict[str, Any]],
    task_set_id: ObjectId
) -> List[ObjectId]:
    """
    Save task items to curated_content_items collection following v2 pattern.
    Media generation happens AFTER insertion, not during.

    Args:
        current_user: User context
        task_items: List of task item data
        task_set_id: Content set ID

    Returns:
        List of ObjectIds of saved task items
    """
    try:
        saved_task_ids = []

        for task_item in task_items:
            task_item_id = ObjectId()
            # Create task item document following exact v2 task_items structure
            task_item_doc = {
                "_id": task_item_id,
                "content_set_id": task_set_id,  # Link to content set
                "type": task_item["type"],
                "title": task_item["title"],
                "question": task_item["question"],
                "correct_answer": task_item["correct_answer"],
                "user_answer": task_item["user_answer"],
                "status": task_item["status"],
                "result": task_item["result"],
                "remark": task_item["remark"],
                "total_score": task_item["total_score"],
                "scored": task_item["scored"],
                "submitted": task_item["submitted"],
                "submitted_at": task_item["submitted_at"],
                "attempts_count": task_item["attempts_count"],
                "difficulty_level": task_item["difficulty_level"],
                "metadata": task_item["metadata"],
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert task item
            await current_user.async_db.curated_content_items.insert_one(task_item_doc)
            saved_task_ids.append(task_item_id)
            loggers.info(f"Saved task item {task_item['title']} with ID: {task_item_id}")

        loggers.info(f"Saved {len(saved_task_ids)} task items")
        return saved_task_ids

    except Exception as e:
        loggers.error(f"Error saving task items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task items: {str(e)}")


async def save_story_items_to_db(
    current_user: UserTenantDB,
    story_items: List[Dict[str, Any]],
    task_set_id: ObjectId
) -> List[ObjectId]:
    """
    Save story items to curated_content_story collection following v2 pattern.
    Media generation happens AFTER insertion, not during.

    Args:
        current_user: User context
        story_items: List of story item data
        content_set_id: Content set ID

    Returns:
        List of ObjectIds of saved story items
    """
    try:
        saved_story_ids = []

        for story_item in story_items:
            story_item_id = ObjectId()

            # Create story item document following exact v2 story_steps structure
            story_item_doc = {
                "_id": story_item_id,
                "type": story_item["type"],  # Include type field for curated content stories
                "user_id": str(current_user.user.id),
                "stage": story_item["stage"],
                "script": story_item["script"],
                "image": story_item["image"],
                "thumbnail": story_item["thumbnail"],
                "audio_metadata": story_item["audio_metadata"],
                "image_metadata": story_item["image_metadata"],
                "metadata": story_item["metadata"],  # Include universal status metadata
                "content_set_id": task_set_id,  # Link to content set
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert story item to curated_content_story collection
            await current_user.async_db.curated_content_story.insert_one(story_item_doc)
            saved_story_ids.append(story_item_id)
            loggers.info(f"Saved story item stage {story_item['stage']} with ID: {story_item_id} to curated_content_story")

        loggers.info(f"Saved {len(saved_story_ids)} story items to curated_content_story collection")
        return saved_story_ids

    except Exception as e:
        loggers.error(f"Error saving story items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save story items: {str(e)}")


async def generate_media_for_tasks_and_stories(
    current_user: UserTenantDB,
    task_item_ids: List[ObjectId],
    story_item_ids: List[ObjectId],
    generate_image_func,
    generate_audio_task_func
):
    """
    Generate media for task items and story items AFTER they are saved to database.
    This follows the v2 pattern where items are inserted first, then media is generated.

    Args:
        current_user: User context
        task_item_ids: List of task item ObjectIds
        story_item_ids: List of story item ObjectIds
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function
    """
    try:
        loggers.info(f"Starting background media generation for {len(task_item_ids)} tasks and {len(story_item_ids)} stories")

        # Generate media for task items
        for task_id in task_item_ids:
            try:
                # Get task item from database
                task_item = await current_user.async_db.curated_content_items.find_one({"_id": task_id})
                if not task_item:
                    continue

                task_type = task_item.get("type", "")
                question = task_item.get("question", {})

                # Generate image for image_identification tasks
                if task_type == "image_identification":
                    keyword = question.get("answer_hint", "")
                    if keyword:
                        try:
                            loggers.info(f"Generating image for task {task_id} with keyword: {keyword}")
                            _, file_info, usage_metadata = await generate_image_func(current_user, keyword)

                            if file_info:
                                # Update task item with image metadata and universal status
                                await current_user.async_db.curated_content_items.update_one(
                                    {"_id": task_id},
                                    {
                                        "$set": {
                                            "question.metadata": file_info,
                                            "metadata._media_ready": True,
                                            "metadata._priority": "image_ready",
                                            "metadata.status": "completed",  # Universal status - completed after successful generation
                                            "updated_at": datetime.now(timezone.utc)
                                        }
                                    }
                                )
                                loggers.info(f"✅ Image generated for task {task_id}")

                                # Check for thumbnail generation after task completion
                                await _check_and_generate_thumbnail_for_editor(current_user, task_id)
                            else:
                                # Image generation failed - update status to failed
                                await current_user.async_db.curated_content_items.update_one(
                                    {"_id": task_id},
                                    {
                                        "$set": {
                                            "metadata.status": "failed",  # Universal status - failed
                                            "metadata.error": f"Image generation failed for keyword: {keyword}",
                                            "updated_at": datetime.now(timezone.utc)
                                        }
                                    }
                                )
                                loggers.error(f"❌ Image generation failed for task {task_id}")
                        except Exception as image_error:
                            # Image generation exception - update status to failed
                            await current_user.async_db.curated_content_items.update_one(
                                {"_id": task_id},
                                {
                                    "$set": {
                                        "metadata.status": "failed",  # Universal status - failed
                                        "metadata.error": f"Image generation error: {str(image_error)}",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.error(f"❌ Image generation exception for task {task_id}: {image_error}")

                # Generate audio for speak_word and word_identification tasks
                elif task_type in ["speak_word", "word_identification"]:
                    keyword = question.get("answer_hint", "")
                    if keyword:
                        try:
                            loggers.info(f"Generating audio for task {task_id} with keyword: {keyword}")
                            _file_text, file_info, usage_metadata = await generate_audio_task_func(current_user, keyword)

                            if file_info:
                                # Store audio file info in metadata (following v2 pattern)
                                audio_metadata = {
                                    **file_info,
                                    "keyword": keyword,
                                    "_audio_ready": True,
                                    "_priority": "audio_ready",
                                    "usage": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else str(usage_metadata),
                                    "generated_at": datetime.now(timezone.utc)
                                }

                                # Update task item with audio metadata and universal status
                                await current_user.async_db.curated_content_items.update_one(
                                    {"_id": task_id},
                                    {
                                        "$set": {
                                            "question.metadata": audio_metadata,
                                            "metadata._media_ready": True,
                                            "metadata._priority": "audio_ready",
                                            "metadata.status": "completed",  # Universal status - completed after successful generation
                                            "updated_at": datetime.now(timezone.utc)
                                        }
                                    }
                                )
                                loggers.info(f"✅ Audio generated for task {task_id}")

                                # Check for thumbnail generation after task completion
                                await _check_and_generate_thumbnail_for_editor(current_user, task_id)
                            else:
                                # Audio generation failed - update status to failed
                                await current_user.async_db.curated_content_items.update_one(
                                    {"_id": task_id},
                                    {
                                        "$set": {
                                            "metadata.status": "failed",  # Universal status - failed
                                            "metadata.error": f"Audio generation failed for keyword: {keyword}",
                                            "updated_at": datetime.now(timezone.utc)
                                        }
                                    }
                                )
                                loggers.error(f"❌ Audio generation failed for task {task_id}")
                        except Exception as audio_error:
                            # Audio generation exception - update status to failed
                            await current_user.async_db.curated_content_items.update_one(
                                {"_id": task_id},
                                {
                                    "$set": {
                                        "metadata.status": "failed",  # Universal status - failed
                                        "metadata.error": f"Audio generation error: {str(audio_error)}",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.error(f"❌ Audio generation exception for task {task_id}: {audio_error}")

            except Exception as e:
                loggers.error(f"Failed to generate media for task {task_id}: {e}")

        # Generate media for story items
        for story_id in story_item_ids:
            try:
                # Get story item from curated_content_story collection
                story_item = await current_user.async_db.curated_content_story.find_one({"_id": story_id})
                if not story_item:
                    continue

                # Generate image for story
                image_description = story_item.get("image", "")
                if image_description:
                    try:
                        loggers.info(f"Generating image for story {story_id} with description: {image_description}")
                        _, file_info, usage_metadata = await generate_image_func(current_user, image_description)

                        if file_info:
                            # Update story item with image metadata and universal status
                            await current_user.async_db.curated_content_story.update_one(
                                {"_id": story_id},
                                {
                                    "$set": {
                                        "image_metadata": {
                                            **file_info,
                                            "_image_ready": True,
                                            "_priority": "image_ready",
                                            "usage": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else str(usage_metadata),
                                            "generated_at": datetime.now(timezone.utc)
                                        },
                                        "metadata.status": "completed",  # Universal status - completed after successful generation
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.info(f"✅ Image generated for story {story_id}")
                        else:
                            # Image generation failed - update status to failed
                            await current_user.async_db.curated_content_story.update_one(
                                {"_id": story_id},
                                {
                                    "$set": {
                                        "metadata.status": "failed",  # Universal status - failed
                                        "metadata.error": f"Image generation failed for description: {image_description}",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.error(f"❌ Image generation failed for story {story_id}")
                    except Exception as image_error:
                        # Image generation exception - update status to failed
                        await current_user.async_db.curated_content_story.update_one(
                            {"_id": story_id},
                            {
                                "$set": {
                                    "metadata.status": "failed",  # Universal status - failed
                                    "metadata.error": f"Image generation error: {str(image_error)}",
                                    "updated_at": datetime.now(timezone.utc)
                                }
                            }
                        )
                        loggers.error(f"❌ Image generation exception for story {story_id}: {image_error}")

                # Generate audio for story
                script_text = story_item.get("script", "")
                if script_text:
                    try:
                        loggers.info(f"Generating audio for story {story_id} with script: {script_text[:50]}...")
                        _file_text, file_info, usage_metadata = await generate_audio_task_func(current_user, script_text)

                        if file_info:
                            # Store audio file info (following v2 pattern)
                            audio_metadata = {
                                **file_info,
                                "script": script_text,
                                "_audio_ready": True,
                                "_priority": "audio_ready",
                                "usage": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else str(usage_metadata),
                                "generated_at": datetime.now(timezone.utc)
                            }

                            # Update story item with audio metadata and universal status
                            await current_user.async_db.curated_content_story.update_one(
                                {"_id": story_id},
                                {
                                    "$set": {
                                        "audio_metadata": audio_metadata,
                                        "metadata.status": "completed",  # Universal status - completed after successful generation
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.info(f"✅ Audio generated for story {story_id}")

                            # Check for thumbnail generation after story completion
                            await _check_and_generate_thumbnail_for_editor(current_user, story_id)
                        else:
                            # Audio generation failed - update status to failed
                            await current_user.async_db.curated_content_story.update_one(
                                {"_id": story_id},
                                {
                                    "$set": {
                                        "metadata.status": "failed",  # Universal status - failed
                                        "metadata.error": f"Audio generation failed for script: {script_text[:50]}...",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.error(f"❌ Audio generation failed for story {story_id}")
                    except Exception as audio_error:
                        # Audio generation exception - update status to failed
                        await current_user.async_db.curated_content_story.update_one(
                            {"_id": story_id},
                            {
                                "$set": {
                                    "metadata.status": "failed",  # Universal status - failed
                                    "metadata.error": f"Audio generation error: {str(audio_error)}",
                                    "updated_at": datetime.now(timezone.utc)
                                }
                            }
                        )
                        loggers.error(f"❌ Audio generation exception for story {story_id}: {audio_error}")

            except Exception as e:
                loggers.error(f"Failed to generate media for story {story_id}: {e}")

        loggers.info("✅ Background media generation completed")

    except Exception as e:
        loggers.error(f"Error in background media generation: {e}")


async def _check_and_generate_thumbnail_for_editor(current_user: UserTenantDB, item_id: ObjectId):
    """
    Check if all tasks and stories in an editor content set are completed and generate thumbnail if needed.

    Args:
        current_user: User context with database access
        item_id: The task ID or story ID that just completed
    """
    try:
        # Import the thumbnail functions from task_utils_v2
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import (
            _are_all_tasks_completed,
            _are_all_stories_completed,
            _generate_and_store_thumbnail
        )

        # Try to find the item in task items first
        item = await current_user.async_db.curated_content_items.find_one(
            {"_id": item_id},
            {"content_set_id": 1}
        )

        # If not found in task items, try story items
        if not item:
            item = await current_user.async_db.curated_content_story.find_one(
                {"_id": item_id},
                {"content_set_id": 1}
            )

        if not item or not item.get("content_set_id"):
            return

        content_set_id = item["content_set_id"]

        # Get the content set with thumbnail info
        content_set = await current_user.async_db.curated_content_set.find_one(
            {"_id": content_set_id},
            {"tasks": 1, "stories": 1, "thumbnail": 1, "thumbnail_metadata": 1}
        )

        if not content_set:
            return

        # Skip if thumbnail already exists
        if content_set.get("thumbnail_metadata"):
            return

        # Skip if no thumbnail keyword is set
        thumbnail_keyword = content_set.get("thumbnail")
        if not thumbnail_keyword:
            return

        # Check if all tasks are completed
        all_tasks_completed = await _are_all_tasks_completed(current_user, content_set.get("tasks", []))

        # Check if all stories are completed
        all_stories_completed = await _are_all_stories_completed(current_user, content_set.get("stories", []))

        loggers.info(f"🔍 THUMBNAIL CHECK | CONTENT SET {content_set_id} | Thumbnail keyword: {thumbnail_keyword}")
        loggers.info(f"🔍 THUMBNAIL CHECK | CONTENT SET {content_set_id} | Tasks completed: {all_tasks_completed} | Stories completed: {all_stories_completed}")

        # Generate thumbnail if all tasks and stories are completed
        if all_tasks_completed and all_stories_completed:
            loggers.info(f"🖼️ All tasks and stories completed for content set {content_set_id}. Generating thumbnail...")
            await _generate_and_store_thumbnail(current_user, str(content_set_id), thumbnail_keyword)
        else:
            loggers.info(f"🔍 THUMBNAIL CHECK | CONTENT SET {content_set_id} | Not ready - Tasks: {all_tasks_completed}, Stories: {all_stories_completed}")

    except Exception as e:
        loggers.error(f"❌ Failed to check and generate thumbnail for editor content: {e}")